<?php
defined('BASEPATH') OR exit('No direct script access allowed');

require_once( dirname(__FILE__) . "/../../../vendor/autoload.php");

require_once(dirname(__FILE__) . '/AuthApi.php');

class MenuApi extends AuthApi {

  public function __construct()
  {
    parent::__construct();
    $this->load->model(array('menu_model', 'document_model', 'folder_model', 'group_model'));
    
    // Suppress all PHP warnings and errors for clean JSON output
    error_reporting(E_ERROR);
    ini_set('display_errors', 0);
    
    // Set error handler to ensure JSON response for any PHP errors
    set_error_handler(array($this, 'handlePhpError'));
  }

  // Custom error handler to ensure JSON responses
  public function handlePhpError($severity, $message, $file, $line) {
    // Only handle if this is our API
    if (strpos($file, 'MenuApi.php') !== false) {
      $this->output
        ->set_status_header(500)
        ->set_content_type('application/json')
        ->set_output(json_encode([
          'success' => false,
          'error' => 'Internal server error',
          'message' => 'An error occurred while processing your request'
        ]));
      exit();
    }
    return false; // Let PHP handle other errors normally
  }

  public function getMainMenus() {
    if ($this->validate(false)) {
      try {
        $result = array();
        
        // Use proper security system - get menus based on user's groups and permissions
        $accessible_menus = $this->menu_model->get_by_groups_bin(
          $this->groups['types']['bin']['position'],
          $this->groups['types']['bin']['department'], 
          $this->acl['object']
        );
        
        if (empty($accessible_menus) || empty($accessible_menus['structure'])) {
          return $this->output
            ->set_status_header(404)
            ->set_content_type('application/json')
            ->set_output(json_encode([
              'success' => false,
              'error' => 'No menus found',
              'message' => 'No accessible menus found for this user'
            ]));
        }
        
        // Get top level menus (parent_id = 0)
        if (isset($accessible_menus['structure'][0]) && !empty($accessible_menus['structure'][0])) {
          foreach ($accessible_menus['structure'][0] as $menu_id => $menu) {
            $result[] = array(
              'id' => $menu->menu_id,
              'name' => $menu->name,
              'type' => 'menu'
            );
          }
        }

        if (empty($result)) {
          return $this->output
            ->set_status_header(404)
            ->set_content_type('application/json')
            ->set_output(json_encode([
              'success' => false,
              'error' => 'No main menus found',
              'message' => 'No main level menus are available'
            ]));
        }

        return $this->output
          ->set_status_header(200)
          ->set_content_type('application/json')
          ->set_output(json_encode([
            'success' => true,
            'menus' => $result,
            'total' => count($result)
          ]));
          
      } catch (Exception $e) {
        error_log("MenuApi::getMainMenus Error: " . $e->getMessage());
        return $this->output
          ->set_status_header(500)
          ->set_content_type('application/json')
          ->set_output(json_encode([
            'success' => false,
            'error' => 'Internal server error',
            'message' => 'An error occurred while loading menus'
          ]));
      }
    } else {
      // Authentication failed - handled by validate() method
      return;
    }
  }

  public function getMenuStructure($menu_id) {
    if ($this->validate(false)) {
      try {
        
        // Validate menu_id parameter
        if (empty($menu_id)) {
          return $this->output
            ->set_status_header(400)
            ->set_content_type('application/json')
            ->set_output(json_encode([
              'success' => false,
              'error' => 'Menu ID is required',
              'message' => 'Parameter "id" is missing'
            ]));
        }

        // Validate UUID format - with additional length check
        if (!VALID_UUIDv4($menu_id) || strlen($menu_id) !== 36) {
          return $this->output
            ->set_status_header(400)
            ->set_content_type('application/json')
            ->set_output(json_encode([
              'success' => false,
              'error' => 'Invalid UUID format',
              'message' => 'Menu ID must be a valid UUID (36 characters with dashes)'
            ]));
        }

        // Check if menu exists and user has access
        $menu_exists = $this->validateMenuAccess($menu_id);
        if (!$menu_exists) {
          return $this->output
            ->set_status_header(404)
            ->set_content_type('application/json')
            ->set_output(json_encode([
              'success' => false,
              'error' => 'Menu not found',
              'message' => 'The specified menu does not exist or you do not have access to it'
            ]));
        }

        $result = array();
        
        // Get accessible menus using proper security system
        $accessible_menus = $this->menu_model->get_by_groups_bin(
          $this->groups['types']['bin']['position'],
          $this->groups['types']['bin']['department'], 
          $this->acl['object']
        );
        
        if (empty($accessible_menus) || !isset($accessible_menus['all'][$menu_id])) {
          return $this->output
            ->set_status_header(403)
            ->set_content_type('application/json')
            ->set_output(json_encode([
              'success' => false,
              'error' => 'Access denied',
              'message' => 'You do not have permission to access this menu'
            ]));
        }
        
        $all_menu_ids = [$menu_id];
        $all_menus = [];
        
        // Get the root menu first for name resolution
        $root_menu = $accessible_menus['all'][$menu_id];
        if ($root_menu) {
          $all_menus[0] = [$menu_id => $root_menu];
        }
        
        // 1. Get first level sub-menus from accessible menus only
        $sub_menus = [];
        if (isset($accessible_menus['structure'][$menu_id])) {
          $sub_menus = $accessible_menus['structure'][$menu_id];
        }
        
        if (!empty($sub_menus)) {
          $all_menus[1] = [];
          foreach ($sub_menus as $menu_obj) {
            $all_menu_ids[] = $menu_obj->menu_id;
            $all_menus[1][$menu_obj->menu_id] = $menu_obj;
            
            // Get parent menu name
            $parent_menu_name = '';
            if (isset($all_menus[0][$menu_obj->parent_id])) {
              $parent_menu_name = $all_menus[0][$menu_obj->parent_id]->name;
            }
            
            $result[] = array(
              'id' => $menu_obj->menu_id,
              'name' => $menu_obj->name,
              'type' => 'menu',
              'parent_id' => $menu_obj->parent_id,
              'parent_name' => $parent_menu_name,
              'level' => 1
            );
            
            // Get second level sub-menus (like mmm2) from accessible menus only
            $sub_sub_menus = [];
            if (isset($accessible_menus['structure'][$menu_obj->menu_id])) {
              $sub_sub_menus = $accessible_menus['structure'][$menu_obj->menu_id];
            }
            
            if (!empty($sub_sub_menus)) {
              if (!isset($all_menus[2])) {
                $all_menus[2] = [];
              }
              foreach ($sub_sub_menus as $sub_menu) {
                $all_menu_ids[] = $sub_menu->menu_id;
                $all_menus[2][$sub_menu->menu_id] = $sub_menu;
                $result[] = array(
                  'id' => $sub_menu->menu_id,
                  'name' => $sub_menu->name,
                  'type' => 'menu',
                  'parent_id' => $sub_menu->parent_id,
                  'parent_name' => $menu_obj->name, // Parent is the first level sub-menu
                  'level' => 2
                );
              }
            }
          }
        }

        // 2. Get folders under ALL menus (main + sub-menus + sub-sub-menus)
        $folders = [];
        foreach ($all_menu_ids as $current_menu_id) {
          try {
            $menu_folders = $this->folder_model->get_all($current_menu_id);
            if (!empty($menu_folders)) {
              foreach ($menu_folders as $folder) {
                $folder->source_menu_id = $current_menu_id;
                $folders[] = $folder;
              }
            }
          } catch (Exception $e) {
            error_log("Error loading folders for menu {$current_menu_id}: " . $e->getMessage());
            // Continue with other menus
          }
        }
        
        if (!empty($folders)) {
          foreach ($folders as $folder) {
            // Determine folder level and get parent menu name
            $folder_level = 1;
            $parent_menu_name = '';
            
            // Find the menu this folder belongs to
            $found_menu = null;
            
            // Check root menu first
            if (isset($all_menus[0][$folder->menu_id])) {
              $found_menu = $all_menus[0][$folder->menu_id];
              $folder_level = 1;
            }
            // Check level 1 sub-menus
            elseif (isset($all_menus[1][$folder->menu_id])) {
              $found_menu = $all_menus[1][$folder->menu_id];
              $folder_level = 2;
            }
            // Check level 2 sub-menus
            elseif (isset($all_menus[2]) && isset($all_menus[2][$folder->menu_id])) {
              $found_menu = $all_menus[2][$folder->menu_id];
              $folder_level = 3;
            }
            
            if ($found_menu) {
              $parent_menu_name = $found_menu->name;
            }
            
            if ($folder->source_menu_id !== $menu_id) {
              // Check if it's from a sub-menu
              foreach ($sub_menus as $sub_menu) {
                if ($folder->source_menu_id === $sub_menu->menu_id) {
                  $folder_level = 2;
                  $parent_menu_name = $sub_menu->name;
                  break;
                }
              }
              // Check if it's from a sub-sub-menu
              foreach ($sub_menus as $sub_menu) {
                $sub_sub_menus = $this->menu_model->get_all_by_parent_id($sub_menu->menu_id);
                if (!empty($sub_sub_menus)) {
                  foreach ($sub_sub_menus as $sub_sub_menu) {
                    if ($folder->source_menu_id === $sub_sub_menu->menu_id) {
                      $folder_level = 3;
                      $parent_menu_name = $sub_sub_menu->name;
                      break 2;
                    }
                  }
                }
              }
            }
            
            $result[] = array(
              'id' => $folder->folder_id,
              'name' => $folder->name,
              'type' => 'folder',
              'parent_id' => $folder->menu_id,
              'parent_name' => $parent_menu_name,
              'menu_id' => $folder->menu_id,
              'menu_name' => $parent_menu_name,
              'level' => $folder_level
            );
            
            // 3. Get documents in this folder
            try {
              $documents = $this->document_model->get_all_documents_in_folder($folder->folder_id);
              if (!empty($documents)) {
                foreach ($documents as $doc) {
                  $result[] = array(
                    'id' => $doc->document_id,
                    'name' => $doc->name,
                    'type' => 'document',
                    'parent_id' => $folder->folder_id,
                    'parent_name' => $folder->name,
                    'folder_id' => $folder->folder_id,
                    'folder_name' => $folder->name,
                    'menu_id' => $folder->menu_id,
                    'menu_name' => $parent_menu_name,
                    'status' => isset($doc->status) ? $doc->status : 'unknown',
                    'level' => $folder_level + 1
                  );
                }
              }
            } catch (Exception $e) {
              error_log("Error loading documents for folder {$folder->folder_id}: " . $e->getMessage());
              // Continue with other folders
            }
          }
        }

        return $this->output
          ->set_status_header(200)
          ->set_content_type('application/json')
          ->set_output(json_encode([
            'success' => true,
            'menu_id' => $menu_id,
            'items' => $result,
            'total' => count($result),
            'structure' => [
              'sub_menus' => count(array_filter($result, function($item) { return $item['type'] === 'menu'; })),
              'folders' => count(array_filter($result, function($item) { return $item['type'] === 'folder'; })),
              'documents' => count(array_filter($result, function($item) { return $item['type'] === 'document'; }))
            ]
          ]));
          
      } catch (Exception $e) {
        error_log("MenuApi::getMenuStructure Error: " . $e->getMessage());
        return $this->output
          ->set_status_header(500)
          ->set_content_type('application/json')
          ->set_output(json_encode([
            'success' => false,
            'error' => 'Internal server error',
            'message' => 'An error occurred while processing your request'
          ]));
      }
    } else {
      // Authentication failed - handled by validate() method
      return;
    }
  }

  public function getFolderDocuments( $folder_id ) {
    if ($this->validate(false)) {
      try {
        
        // Validate folder_id parameter
        if (empty($folder_id)) {
          return $this->output
            ->set_status_header(400)
            ->set_content_type('application/json')
            ->set_output(json_encode([
              'success' => false,
              'error' => 'Folder ID is required',
              'message' => 'Parameter "id" is missing'
            ]));
        }

        // Validate UUID format - with additional length check
        if (!VALID_UUIDv4($folder_id) || strlen($folder_id) !== 36) {
          return $this->output
            ->set_status_header(400)
            ->set_content_type('application/json')
            ->set_output(json_encode([
              'success' => false,
              'error' => 'Invalid UUID format',
              'message' => 'Folder ID must be a valid UUID (36 characters with dashes)'
            ]));
        }

        // Check if folder exists and user has access
        $folder_exists = $this->validateFolderAccess($folder_id);
        if (!$folder_exists) {
          return $this->output
            ->set_status_header(404)
            ->set_content_type('application/json')
            ->set_output(json_encode([
              'success' => false,
              'error' => 'Folder not found',
              'message' => 'The specified folder does not exist or you do not have access to it'
            ]));
        }

        $result = array();
        
        // Get folder name first - using secure method
        $folder_name = '';
        $folder_menu_id = '';
        try {
          // Get accessible menus using proper security system
          $accessible_menus = $this->menu_model->get_by_groups_bin(
            $this->groups['types']['bin']['position'],
            $this->groups['types']['bin']['department'], 
            $this->acl['object']
          );
          
          if (!empty($accessible_menus['all'])) {
            foreach ($accessible_menus['all'] as $menu_id => $menu) {
              if (isset($menu->folders) && !empty($menu->folders)) {
                foreach ($menu->folders as $folder) {
                  if ($folder->folder_id === $folder_id) {
                    $folder_name = $folder->name;
                    $folder_menu_id = $menu_id;
                    break 2;
                  }
                }
              }
            }
          }
        } catch (Exception $e) {
          error_log("Error getting folder name: " . $e->getMessage());
        }
        
        // Get documents in this folder
        try {
          $documents = $this->document_model->get_all_documents_in_folder($folder_id);
          if (!empty($documents)) {
            foreach ($documents as $doc) {
              $result[] = array(
                'id' => $doc->document_id,
                'name' => $doc->name,
                'type' => 'document',
                'folder_id' => $folder_id,
                'folder_name' => $folder_name,
                'status' => isset($doc->status) ? $doc->status : 'unknown',
                'created_date' => isset($doc->created_date) ? $doc->created_date : null,
                'edited_date' => isset($doc->edited_date) ? $doc->edited_date : null,
                'created_by' => isset($doc->created_by) ? $doc->created_by : null,
                'edited_by' => isset($doc->edited_by) ? $doc->edited_by : null
              );
            }
          }
        } catch (Exception $e) {
          error_log("Error loading documents for folder {$folder_id}: " . $e->getMessage());
          return $this->output
            ->set_status_header(500)
            ->set_content_type('application/json')
            ->set_output(json_encode([
              'success' => false,
              'error' => 'Database error',
              'message' => 'An error occurred while loading documents from the folder'
            ]));
        }

        return $this->output
          ->set_status_header(200)
          ->set_content_type('application/json')
          ->set_output(json_encode([
            'success' => true,
            'folder_id' => $folder_id,
            'documents' => $result,
            'total' => count($result)
          ]));
          
      } catch (Exception $e) {
        error_log("MenuApi::getFolderDocuments Error: " . $e->getMessage());
        return $this->output
          ->set_status_header(500)
          ->set_content_type('application/json')
          ->set_output(json_encode([
            'success' => false,
            'error' => 'Internal server error',
            'message' => 'An error occurred while processing your request'
          ]));
      }
    } else {
      // Authentication failed - handled by validate() method
      return;
    }
  }

//getDocumentById 
  public function getDocumentById( $document_id ) {
    if ($this->validate(false)) {
      try {
        
        // Validate document_id parameter
        if (empty($document_id)) {
          return $this->output
            ->set_status_header(400)
            ->set_content_type('application/json')
            ->set_output(json_encode([
              'success' => false,
              'error' => 'Document ID is required',
              'message' => 'Parameter "id" is missing'
            ]));
        }

        // Validate UUID format - with additional length check
        if (!VALID_UUIDv4($document_id) || strlen($document_id) !== 36) {
          return $this->output
            ->set_status_header(400)
            ->set_content_type('application/json')
            ->set_output(json_encode([
              'success' => false,
              'error' => 'Invalid UUID format',
              'message' => 'Document ID must be a valid UUID (36 characters with dashes)'
            ]));
        }

        // Check if document exists and user has access
        $document_exists = $this->validateDocumentAccess($document_id);
        if (!$document_exists) {
          return $this->output
            ->set_status_header(404)
            ->set_content_type('application/json')
            ->set_output(json_encode([
              'success' => false,
              'error' => 'Document not found',
              'message' => 'The specified document does not exist or you do not have access to it'
            ]));
        }

        // Get document details - but only after confirming access
        $document = $this->document_model->get($document_id);
        if (empty($document)) {
          return $this->output
            ->set_status_header(404)
            ->set_content_type('application/json')
            ->set_output(json_encode([
              'success' => false,
              'error' => 'Document not found',
              'message' => 'Document not found in database'
            ]));
        }

        // Double-check: Verify document's folder is in accessible menus
        $accessible_menus = $this->menu_model->get_by_groups_bin(
          $this->groups['types']['bin']['position'],
          $this->groups['types']['bin']['department'], 
          $this->acl['object']
        );
        
        $has_folder_access = false;
        if (!empty($accessible_menus['all'])) {
          foreach ($accessible_menus['all'] as $menu_id => $menu) {
            if (isset($menu->folders) && !empty($menu->folders)) {
              foreach ($menu->folders as $folder) {
                if ($folder->folder_id === $document->folder_id) {
                  $has_folder_access = true;
                  break 2;
                }
              }
            }
          }
        }
        
        if (!$has_folder_access) {
          return $this->output
            ->set_status_header(403)
            ->set_content_type('application/json')
            ->set_output(json_encode([
              'success' => false,
              'error' => 'Access denied',
              'message' => 'You do not have permission to access this document'
            ]));
        }

        // Get folder information
        $folder = $this->folder_model->get($document->folder_id);
        if (empty($folder)) {
          return $this->output
            ->set_status_header(404)
            ->set_content_type('application/json')
            ->set_output(json_encode([
              'success' => false,
              'error' => 'Folder not found',
              'message' => 'Document folder not found'
            ]));
        }

        // Get menu information (parent menu)
        $parent_menu = $this->menu_model->get($folder->menu_id);
        if (empty($parent_menu)) {
          return $this->output
            ->set_status_header(404)
            ->set_content_type('application/json')
            ->set_output(json_encode([
              'success' => false,
              'error' => 'Menu not found',
              'message' => 'Document menu not found'
            ]));
        }

        // Get main menu (root menu)
        $main_menu = null;
        $main_menu_name = '';
        if ($parent_menu->parent_id) {
          // Find the root menu by traversing up the hierarchy
          $current_menu = $parent_menu;
          while ($current_menu->parent_id) {
            $current_menu = $this->menu_model->get($current_menu->parent_id);
            if (empty($current_menu)) {
              break;
            }
          }
          $main_menu = $current_menu;
          $main_menu_name = $main_menu ? $main_menu->name : '';
        } else {
          $main_menu = $parent_menu;
          $main_menu_name = $parent_menu->name;
        }

        // Get main attachment information
        $main_attachment = $this->document_model->get_main_attachment($document_id);
        $main_attachment_info = null;
        if ($main_attachment) {
          $main_attachment_info = $main_attachment->file_name;
        }

        // Get creator information
        $this->load->model('user_model');
        $created_by_name = '';
        $edited_by_name = '';
        if ($document->created_by) {
          try {
            $created_by_user = $this->user_model->get($document->created_by);
            if ($created_by_user) {
              $created_by_name = $created_by_user->name;
            }
            if (isset($document->created_by)) {
              $edited_by_user = $this->user_model->get($document->edited_by);
              if ($edited_by_user) {
                $edited_by_name = $edited_by_user->name;
              }
            }
          } catch (Exception $e) {
            error_log("Error getting creator information: " . $e->getMessage());
          }
        }

        // Prepare response
        $this->_get_document_type_data();
		    $this->data['documents_category'] = $this->document_model->get_document_category();
        $document_type = $this->data['documents_type'][$document->document_type];
        $document_category = $this->data['documents_category'][$document->document_category];
        $attachments = $this->document_model->get_attachments($document->document_id);
        $base_url = serverPath() . "/documentcallback/download_path/" . $this->auth_company_id;
        $result = [
          'id' => $document->document_id,
          'name' => $document->name,
          'status' => $document->status,
          'created_date' => $document->created_date,
          'created_by' => $created_by_name,
          'edited_date' => $document->edited_date,
          'edited_by' => $edited_by_name,
          'description' => $document->description,
          'document_type' => $document_type,
          'document_category' => $document_category,
          'valid_until' => $document->valid_until,
          'content' => $document->content,
          'attachments' => isset($attachments) ? $attachments : [],
          'base_url' => $base_url,
          'folder' => [
            'id' => $folder->folder_id,
            'name' => $folder->name
          ],
          'parent_menu' => [
            'id' => $parent_menu->menu_id,
            'name' => $parent_menu->name
          ],
          'main_menu' => [
            'id' => $main_menu ? $main_menu->menu_id : $parent_menu->menu_id,
            'name' => $main_menu_name
          ],
          'main_attachment' => $main_attachment_info
        ];

        return $this->output
          ->set_status_header(200)
          ->set_content_type('application/json')
          ->set_output(json_encode([
            'success' => true,
            'document' => $result
          ]));
          
      } catch (Exception $e) {
        error_log("MenuApi::getDocumentById Error: " . $e->getMessage());
        return $this->output
          ->set_status_header(500)
          ->set_content_type('application/json')
          ->set_output(json_encode([
            'success' => false,
            'error' => 'Internal server error',
            'message' => 'An error occurred while processing your request'
          ]));
      }
    } else {
      // Authentication failed - handled by validate() method
      return;
    }
  }

  // Helper method to get all accessible menu IDs for the authenticated user
  private function getAccessibleMenuIds() {
    try {
      $menu_ids = [];
      
      // Use the same security system as the main application
      // This checks user's groups permissions properly
      $accessible_menus = $this->menu_model->get_by_groups_bin(
        $this->groups['types']['bin']['position'],
        $this->groups['types']['bin']['department'], 
        $this->acl['object']
      );
      
      // Extract menu IDs from accessible menus
      if (!empty($accessible_menus['all'])) {
        foreach ($accessible_menus['all'] as $menu_id => $menu) {
          $menu_ids[] = $menu_id;
        }
      }
      
      return $menu_ids;
    } catch (Exception $e) {
      error_log("Error getting accessible menu IDs: " . $e->getMessage());
      return [];
    }
  }

  // Helper method to validate menu access
  private function validateMenuAccess($menu_id) {
    try {
      // Use proper security system - get menus based on user's groups and permissions
      $accessible_menus = $this->menu_model->get_by_groups_bin(
        $this->groups['types']['bin']['position'],
        $this->groups['types']['bin']['department'], 
        $this->acl['object']
      );
      
      // Check if the requested menu ID is in the accessible menus
      return !empty($accessible_menus['all']) && isset($accessible_menus['all'][$menu_id]);
    } catch (Exception $e) {
      error_log("Error validating menu access for {$menu_id}: " . $e->getMessage());
      return false;
    }
  }

  // Helper method to validate folder access
  private function validateFolderAccess($folder_id) {
    try {
      // Use proper security system - get menus based on user's groups and permissions
      $accessible_menus = $this->menu_model->get_by_groups_bin(
        $this->groups['types']['bin']['position'],
        $this->groups['types']['bin']['department'], 
        $this->acl['object']
      );
      
      if (empty($accessible_menus['all'])) {
        return false;
      }
      
      // Check if folder exists in any accessible menu
      foreach ($accessible_menus['all'] as $menu_id => $menu) {
        if (isset($menu->folders) && !empty($menu->folders)) {
          foreach ($menu->folders as $folder) {
            if ($folder->folder_id === $folder_id) {
              return true;
            }
          }
        }
      }
      
      return false;
    } catch (Exception $e) {
      error_log("Error validating folder access for {$folder_id}: " . $e->getMessage());
      return false;
    }
  }

  // Helper method to validate document access
  private function validateDocumentAccess($document_id) {
    try {
      // Use proper security system - get menus based on user's groups and permissions
      $accessible_menus = $this->menu_model->get_by_groups_bin(
        $this->groups['types']['bin']['position'],
        $this->groups['types']['bin']['department'],
        $this->acl['object']
      );

      if (empty($accessible_menus['all'])) {
        return false;
      }

      // Check if document exists in any accessible folder within accessible menus
      foreach ($accessible_menus['all'] as $menu_id => $menu) {
        if (isset($menu->folders) && !empty($menu->folders)) {
          foreach ($menu->folders as $folder) {
            try {
              $documents = $this->document_model->get_all_documents_in_folder($folder->folder_id);
              if (!empty($documents)) {
                foreach ($documents as $document) {
                  if ($document->document_id === $document_id) {
                    return true;
                  }
                }
              }
            } catch (Exception $e) {
              error_log("Error checking documents for folder {$folder->folder_id}: " . $e->getMessage());
              // Continue checking other folders
            }
          }
        }
      }

      return false;
    } catch (Exception $e) {
      error_log("Error validating document access for {$document_id}: " . $e->getMessage());
      return false;
    }
  }

  // Search for documents API endpoint
  public function searchDocuments() {
    if ($this->validate(false)) {
      try {
        // Get search parameter
        $search_query = $this->input->get('s');

        // Validate search parameter
        if (empty($search_query)) {
          return $this->output
            ->set_status_header(400)
            ->set_content_type('application/json')
            ->set_output(json_encode([
              'success' => false,
              'error' => 'Search parameter is required',
              'message' => 'Parameter "s" is missing'
            ]));
        }

        // Validate minimum length (3 characters)
        if (strlen(trim($search_query)) < 3) {
          return $this->output
            ->set_status_header(400)
            ->set_content_type('application/json')
            ->set_output(json_encode([
              'success' => false,
              'error' => 'Search query too short',
              'message' => 'Search query must be at least 3 characters long'
            ]));
        }

        // Validate maximum length (64 characters like in Documents controller)
        if (strlen(trim($search_query)) > 64) {
          return $this->output
            ->set_status_header(400)
            ->set_content_type('application/json')
            ->set_output(json_encode([
              'success' => false,
              'error' => 'Search query too long',
              'message' => 'Search query must be at most 64 characters long'
            ]));
        }

        // Use the existing document_model->search method
        $search_results = $this->document_model->search(
          explode(' ', trim($search_query)),
          array_merge($this->groups['types']['bin']['position'], $this->groups['types']['bin']['department']),
          $this->groups['types']['bin']['position'],
          $this->groups['types']['bin']['department']
        );

        $result = [];
        if (!empty($search_results)) {
          foreach ($search_results as $document) {
            $result[] = [
              'id' => $document->document_id,
              'name' => $document->document_name,
              'description' => $document->document_description,
              'folder_name' => $document->folder_name,
              'menu_name' => $document->menu_name,
              'created_date' => $document->document_created,
              'edited_date' => $document->edited_date,
              'valid_until' => $document->document_valid_until,
              'document_type' => $document->document_type,
              'document_category' => $document->document_category
            ];
          }
        }

        return $this->output
          ->set_status_header(200)
          ->set_content_type('application/json')
          ->set_output(json_encode([
            'success' => true,
            'search_query' => $search_query,
            'documents' => $result,
            'total' => count($result)
          ]));

      } catch (Exception $e) {
        error_log("MenuApi::searchDocuments Error: " . $e->getMessage());
        return $this->output
          ->set_status_header(500)
          ->set_content_type('application/json')
          ->set_output(json_encode([
            'success' => false,
            'error' => 'Internal server error',
            'message' => 'An error occurred while searching documents'
          ]));
      }
    } else {
      // Authentication failed - handled by validate() method
      return;
    }
  }
}
