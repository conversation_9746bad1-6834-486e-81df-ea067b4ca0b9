<?php
require_once( dirname(__FILE__) . '/../libraries/onlyoffice/documentreader.php' );
require_once( dirname(__FILE__) . '/../libraries/onlyoffice/common.php' );

class Document_model extends MY_Model {

	public function __construct()
	{
		parent::__construct();
		$this->load->model(array('deviation_model'));
	}

	public function _get_tree_documents()
	{
		return $this->input->post('tree_documents');
	}

	public function _get_documents_categories_name()
	{
		return trim($this->input->post('documents_categories_name') ?: '');
	}

	public function _get_documents_name()
	{
		return trim($this->input->post('documents_name') ?: '');
	}

	public function _get_documents_description()
	{
		return trim($this->input->post('documents_description') ?: '');
	}

	public function _get_documents_created()
	{
		return trim($this->input->post('documents_created') ?: '');
	}

	public function _get_documents_accepted_by()
	{
		return trim($this->input->post('documents_accepted_by') ?: '');
	}

	public function _get_documents_type()
	{
		return trim($this->input->post('documents_type') ?: '');
	}

	public function _get_documents_category()
	{
		return trim($this->input->post('documents_category') ?: '');
	}

	public function _get_documents_last_revised()
	{
		return trim($this->input->post('documents_last_revised') ?: '');
	}

	public function _get_documents_valid_until()
	{
		return trim($this->input->post('documents_valid_until') ?: '');
	}

	public function _get_documents_reminder()
	{
		return trim($this->input->post('documents_reminder') ?: '');
	}
	// @STEP2: htmlpurifier
	public function _get_documents_document()
	{
		return trim($this->input->post('documents_document') ?: '');
	}

	public function _get_documents_document_clean()
	{
		if( ! empty($this->input->post('documents_document')) )
		{
			return trim(
				preg_replace(
					"/\s+/",
					' ',
					preg_replace(
						"/\r|\n/",
						' ',
						strip_tags($this->input->post('documents_document'))
					)
				)
			);
		}
		return NULL;
	}

	public function _get_documents_owner()
	{
		return trim($this->input->post('documents_owner') ?: '');
	}

	public function _get_documents_position()
	{
		return $this->input->post('documents_position') ? $this->input->post('documents_position') : array();
	}

	public function _get_documents_department()
	{
		return $this->input->post('documents_department') ? $this->input->post('documents_department') : array();
	}

	public function update_content($document_id, $file_path) 
	{
		$docObj = new DocConversion($file_path);
		$content = trim($docObj->convertToText());
		$this->db->update($this->db_table('documents'),['content_clean' => $content], ['document_id' => UUID_TO_BIN($document_id)]);
	}

	public function _get_documents_editors()
	{
		return $this->input->post('documents_editors[]');
	}
	// @STEP2: edited_reason

	// @STEP2: edited_reason
	private function _get_data()
	{
		$data = array(
			'name'		     => $this->_get_documents_name(),
			'description'    => $this->_get_documents_description(),
			'created'		 => $this->_get_documents_created(),
			'accepted_by'	 => $this->_get_documents_accepted_by(),
			'documents_type' => $this->_get_documents_type(),
			'last_revised'   => $this->_get_documents_last_revised(),
			'valid_until'	 => $this->_get_documents_valid_until(),
			'reminder'		 => $this->_get_documents_reminder(),
			'content'		 => $this->_get_documents_document(),
			'owner'		     => $this->_get_documents_owner(),
		);
		return $data;
	}

	public function get( $document_id, $status = array('clone','unpublished','published','draft','edit-draft', 'waiting-approval') )
	{
		$document    = NULL;
		$document_id = UUID_TO_BIN($document_id);

		$q = $this->db
				->where('document_id',$document_id)
				->where_in('status',$status)
				->limit(1)
				->get($this->db_table('documents'));
		if( $q->num_rows() === 1)
		{
			$document = $q->row();
			$document->document_id       = BIN_TO_UUID($document->document_id);
			$document->parent_id         = BIN_TO_UUID($document->parent_id);
			$document->folder_id         = BIN_TO_UUID($document->folder_id);
			$document->created_by        = BIN_TO_UUID($document->created_by);
			$document->edited_by         = BIN_TO_UUID($document->edited_by);
			$document->accepted_by       = BIN_TO_UUID($document->accepted_by);
			$document->document_category = BIN_TO_UUID($document->document_category);
			$document->owner             = BIN_TO_UUID($document->owner);
		}
		return $document;
	}

	public function get_all( $document_id, $status = array('published') )
	{
		// var_dump($document_id);exit;
		$documents    = [];
		$documents_id = [];
		if( is_array($document_id) )
		{
			foreach($document_id as $id)
			{
				$documents_id[] = UUID_TO_BIN($id);
			}
		}
		else
		{
			$documents_id[] = UUID_TO_BIN($document_id);
		}

		$q = $this->db->select('documents.*, document_attachment.file_ext, folders.menu_id, folders.name as folder_name')
				->where_in('documents.document_id',$documents_id)
				->where_in('status',$status)
				->order_by('name','ASC')
				->from( $this->db_table('documents') )
				->join( $this->db_table('folders') , 'documents.folder_id = folders.folder_id', 'left')
				->join( $this->db_table('document_attachment') , "(documents.document_id = document_attachment.document_id and 
					document_attachment.file_name like CONCAT( LOWER( substring(HEX(documents.document_id), 1, 8) ), '%'))", 'left')
				->get();

		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $document)
			{
				$document->document_id       = BIN_TO_UUID($document->document_id);
				$document->parent_id         = BIN_TO_UUID($document->parent_id);
				$document->folder_id         = BIN_TO_UUID($document->folder_id);
				$document->menu_id           = BIN_TO_UUID($document->menu_id);
				$document->created_by        = BIN_TO_UUID($document->created_by);
				$document->edited_by         = BIN_TO_UUID($document->edited_by);
				$document->accepted_by       = BIN_TO_UUID($document->accepted_by);
				$document->document_category = BIN_TO_UUID($document->document_category);
				$document->owner             = BIN_TO_UUID($document->owner);
				$documents[$document->document_id] = $document;
			}
		}

		return $documents;
	}

	public function get_document_exists( $document_id, $version = FALSE )
	{
		$document_id = UUID_TO_BIN($document_id);

		$q = $this->db
				->select('document_id, last_revised')
				->where('document_id',$document_id)
				->limit(1)
				->get($this->db_table('documents'));
		if( $q->num_rows() === 1)
		{
			if( $version )
				return $q->row()->last_revised;
			else
				return TRUE;
		}

		return FALSE;
	}

	public function get_accessible_document( $user_id )
	{
		$owner = '0x'.str_replace('-','',$user_id);
		$q = $this->db->select("documents.document_id as document_id, documents.name as name, acl_actions.action_code as action_code,
				documents.last_revised as last_revised,
				(acl_users_and_groups.object_id = folders.folder_id) as folder_access, folders.folder_id as folder_id,
				(acl_users_and_groups.object_id = menus.menu_id) as menu_access,
				(documents.document_id = document_editor.document_id) as document_editor, menus.menu_id as menu_id,
				(documents.owner = $owner) as document_owner", NULL, FALSE)
			->from( $this->db_table('documents') )
			->join( $this->db_table('folders') , 'documents.folder_id = folders.folder_id', 'left')
			->join( $this->db_table('menus') , 'folders.menu_id = menus.menu_id', 'left')
			->join( $this->db_table('menu_group') , 'menu_group.menu_id = folders.menu_id', 'left')
			->join( $this->db_table('acl_users_and_groups_table') , "( (acl_users_and_groups.user_id = " . $owner .") and 
				((acl_users_and_groups.group_id = menu_group.group_id)
				or (acl_users_and_groups.object_id in ('folders.folder_id', 'menus.menu_id')))", 'left', NULL, FALSE)
			->join( $this->db_table('acl_actions_table') , 'acl_actions.action_id = acl_users_and_groups.action_id', 'left')

			->join( $this->db_table('document_editor') , 'documents.document_id = document_editor.document_id', 'left')
			->where( "documents.name != ''")
			->where( "
				(
					menus.menu_id IN (
						SELECT `menu_id` 
						FROM   `menu_group` 
						WHERE  `group_id` IN (
							SELECT `user_group`.`group_id` as `group_id` from user_group 
							left join  `groups` ON `user_group`.`group_id` = `groups`.`group_id` 
							where user_id = " . $owner . " and `groups`.`type` = 'department'
						)
						GROUP  BY `menu_id`
					) 
					AND 
					menus.menu_id  IN (
						SELECT `menu_id` 
						FROM   `menu_group` 
						WHERE  `group_id` IN (
							SELECT `user_group`.`group_id` as `group_id` from user_group 
							left join  `groups` ON `user_group`.`group_id` = `groups`.`group_id` 
							where user_id = " . $owner . " and `groups`.`type` = 'position'
						)
						GROUP  BY `menu_id`
					)
				)", NULL, FALSE);

		
		$q = $q->where("(documents.owner = " . $owner . " or
			menus.owner = " . $owner . " or
			acl_actions.action_code in ('read','update') or
			document_editor.user_id = " . $owner . " )", NULL, FALSE);
		$q = $q->where_in('status',['published'])
			->order_by('name','ASC')
			->get();
		$documents    = [];
		if( $q->num_rows() !== 0 )
		{
			$result = $q->result();
			$menu_conditions = [];
			$found_menus = [];

			foreach($result as $document)
			{
				$document->document_id       = BIN_TO_UUID($document->document_id);
				$document->menu_id        	 = BIN_TO_UUID($document->menu_id);
				$document->folder_id         = BIN_TO_UUID($document->folder_id);
				$status = $document->action_code == 'read' ? 'read' : 'update';
				$documents[$status][$document->document_id] = $document;
			}
		}

		return $documents;
	}

	public function get_all_by_owner( $owner = NULL, $status = array('unpublished','published', 'draft', 'edit-draft', 'auto-draft', 'waiting-approval') )
	{
		$documents = [];
		$is_array  = FALSE;

		if( is_array($owner) )
		{
			$is_array = TRUE;
			$owners = [];
			foreach($owner as $user_id)
			{
				$owners[] = UUID_TO_BIN($user_id);
			}
			$this->db->where_in('documents.owner',$owners);
		}
		else
		{
			$owner = ! empty($owner) ? $owner : $this->auth_user_id;
		}

		$q = $this->db->select('documents.*, menus.owner as menu_owner, folders.name as folder_name, menus.menu_id as menu_id, 
			document_attachment.file_ext, user_messages.comment as is_denied')
				->from( $this->db_table('documents') )
				->join( $this->db_table('folders') , 'documents.folder_id = folders.folder_id', 'left')
				->join( $this->db_table('menus') , 'folders.menu_id = menus.menu_id', 'left')
				->join( $this->db_table('user_messages') , "user_messages.type_id = documents.document_id and user_messages.type = 'documents'
					and user_messages.action = 'denied'", 'left')
				->join( $this->db_table('document_editor') , 'documents.document_id = document_editor.document_id', 'left')
				->join( $this->db_table('document_attachment') , "(documents.document_id = document_attachment.document_id and 
					document_attachment.file_name like CONCAT( LOWER( substring(HEX(documents.document_id), 1, 8) ), '%'))", 'left')
				->where( "documents.name != ''");

		if ( !is_array($owner) && !is_role('Systemadministratör')) 
		{
			$owner = '0x'.str_replace('-','',$owner);
			$q = $q->where('(documents.owner = ' . $owner . ' or 
				menus.owner = ' . $owner . ' or 
				document_editor.user_id = ' . $owner . ' )', NULL, FALSE);
		}
		$q = $q->where_in('status',$status)
				->order_by('name','ASC')
				->get();

		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $document)
			{
				$document->document_id       = BIN_TO_UUID($document->document_id);
				$document->parent_id         = BIN_TO_UUID($document->parent_id);
				$document->menu_owner        = BIN_TO_UUID($document->menu_owner);
				$document->folder_id         = BIN_TO_UUID($document->folder_id);
				$document->menu_id           = BIN_TO_UUID($document->menu_id);
				$document->created_by        = BIN_TO_UUID($document->created_by);
				$document->edited_by         = BIN_TO_UUID($document->edited_by);
				$document->accepted_by       = BIN_TO_UUID($document->accepted_by);
				$document->document_category = BIN_TO_UUID($document->document_category);
				$document->owner             = BIN_TO_UUID($document->owner);
				$status = in_array($document->status, array('draft', 'edit-draft', 'auto-draft')) ? 'draft' : $document->status;
				if( $is_array )
				{
					$documents[$document->owner][$document->status][$document->document_id] = $document;
				}
				else
				{
					$documents[$document->status][$document->document_id] = $document;
				}
			}
		}

		return $documents;
	}

	public function get_all_by_parent_id( $parent_id, $status = array('archived'), $return = NULL )
	{
		$documents    = [];
		$documents_id = [];
		if( is_array($parent_id) )
		{
			foreach($parent_id as $id)
			{
				$documents_id[] = UUID_TO_BIN($id);
			}
		}
		else
		{
			$documents_id[] = UUID_TO_BIN($parent_id);
		}

		$q = $this->db->select('documents.*, document_attachment.file_ext, folders.menu_id, folders.name as folder_name')
				->where_in('parent_id',$documents_id)
				->where_in('status',$status)
				->order_by('edited_date','DESC')
				->from( $this->db_table('documents') )
				->join( $this->db_table('folders') , 'documents.folder_id = folders.folder_id', 'left')
				->join( $this->db_table('document_attachment') , "(documents.document_id = document_attachment.document_id and 
					document_attachment.file_name like CONCAT( LOWER( substring(HEX(documents.document_id), 1, 8) ), '%'))", 'left')
				->get();

		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $document)
			{
				$document->document_id       = BIN_TO_UUID($document->document_id);
				$document->parent_id         = BIN_TO_UUID($document->parent_id);
				$document->folder_id         = BIN_TO_UUID($document->folder_id);
				$document->menu_id           = BIN_TO_UUID($document->menu_id);
				$document->created_by        = BIN_TO_UUID($document->created_by);
				$document->edited_by         = BIN_TO_UUID($document->edited_by);
				$document->accepted_by       = BIN_TO_UUID($document->accepted_by);
				$document->document_category = BIN_TO_UUID($document->document_category);
				$document->owner             = BIN_TO_UUID($document->owner);
				if( ! empty($return) )
				{
					if( count($status) === 1)
						$documents[$document->{$return}] = $document;
					else
						$documents[$document->status][$document->{$return}] = $document;
				}
				else
				{
					if( count($status) === 1)
						$documents[$document->document_id] = $document;
					else
						$documents[$document->status][$document->document_id] = $document;
				}
			}
		}

		return $documents;
	}

	public function get_draft( $parent_id )
	{
		$document = NULL;
		$parent_id = UUID_TO_BIN($parent_id);

		$q = $this->db
				->where_in('parent_id', $parent_id)
				->where_in('status', ['draft', 'edit-draft', 'auto-draft', 'auto-save', 'waiting-approval'])
				->order_by('edited_date','DESC')
				->get($this->db_table('documents'));

		if( $q->num_rows() !== 0 )
		{
			$document = $q->result()[0];
			$document->document_id = BIN_TO_UUID($document->document_id);
		}
		return $document;
	}

	public function get_inline( $document_id )
	{
		$q = $this->db
				->select('content')
				->where('document_id',UUID_TO_BIN($document_id))
				->limit(1)
				->get($this->db_table('documents'));
		if( $q->num_rows() === 1)
		{
			return $q->row();
		}
		return NULL;
	}

	public function get_attachments( $document_id_raw )
	{
		$attachments = NULL;
		$document_id = UUID_TO_BIN($document_id_raw);

		$q = $this->db
				->select('attachment_id,uploaded_on,file_name,file_ext')
				->where('document_id',$document_id)
				->order_by('uploaded_on','ASC')
				->get($this->db_table('document_attachment'));

		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $attachment)
			{
				if (substr( $attachment->file_name, 0, strlen($document_id_raw) ) !== $document_id_raw) {
					$attachment->attachment_id = BIN_TO_UUID($attachment->attachment_id);
					$attachments[] = $attachment;
				}
			}
		}

		return $attachments;
	}

	public function get_main_attachment( $document_id_raw )
	{
		$attachment = NULL;
		$document_id = UUID_TO_BIN($document_id_raw);

		$q = $this->db
				->select('attachment_id,uploaded_on,file_name,file_ext')
				->where('document_id',$document_id)
				->where("file_name like '". $document_id_raw . "%'")
				->order_by('uploaded_on','ASC')
				->get($this->db_table('document_attachment'));

		if( $q->num_rows() !== 0 )
		{
			$attachment = $q->result()[0];
			$attachment->attachment_id = BIN_TO_UUID($attachment->attachment_id);
		}

		return $attachment;
	}

	public function get_attachment( $attachment_id )
	{
		$attachment = array();
		$attachment_id = UUID_TO_BIN($attachment_id);

		$q = $this->db
				->select('document_id,attachment_id,uploaded_on,file_name,file_ext')
				->where('attachment_id',$attachment_id)
				->limit(1)
				->get($this->db_table('document_attachment'));

		if( $q->num_rows() === 1)
		{
			$attachment = $q->row();
			$attachment->document_id   = BIN_TO_UUID($attachment->document_id);
			$attachment->attachment_id = BIN_TO_UUID($attachment->attachment_id);
		}

		return $attachment;
	}

	// @TODO: Delete with a check (IN ? OR IS NULL)
	// NOT IN USE!
	public function get_attachment_rights( $attachment_id, $groups, $position, $department )
	{
		$query = "
				SELECT *
				FROM  `view_document_attachment`
				WHERE `menu_id` IN (
					SELECT `menu_id`
					FROM   `view_document_attachment`
					WHERE  `document_group_id` IN ?
					GROUP  BY `menu_id`
				)
				AND `menu_id` IN (
					SELECT `menu_id`
					FROM   `view_document_attachment`
					WHERE  `document_group_id` IN ?
					GROUP  BY `menu_id`
				)
				AND `company_id` = ?
				GROUP BY
						`menu_id`
				ORDER BY
						`sticky` ASC,
						`name` ASC
				";

		$attachment = array();
		$attachment_id = UUID_TO_BIN($attachment_id);

		$q = $this->db
				->select('document_id,attachment_id,uploaded_on,file_name,file_ext')
				->where('attachment_id',$attachment_id)
				->limit(1)
				->get($this->db_table('document_attachment'));

		if( $q->num_rows() === 1)
		{
			$attachment = $q->row();
			$attachment->document_id   = BIN_TO_UUID($attachment->document_id);
			$attachment->attachment_id = BIN_TO_UUID($attachment->attachment_id);
		}

		return $attachment;
	}

	public function get_all_documents_by_owner( $owner)
	{
		$documents = array();
		$q = $this->db
				->select('document_id,folder_id,name,description,last_revised,owner,status')
				->where('owner',UUID_TO_BIN($owner))
				->get($this->db_table('documents'));
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $document)
			{
				$document->document_id = BIN_TO_UUID($document->document_id);
				$document->owner       = BIN_TO_UUID($document->owner);
				$documents[$document->document_id] = $document;
			}
		}
		return $documents;
	}

	public function get_all_documents_in_folder( $folder_id, $order_by = 0, $status = array('clone','published'), $with_draft = FALSE )
	{
		$documents = array();
		$order_by = !$order_by ? 'ASC' : 'DESC';
		$is_array = FALSE;

		if( is_array($folder_id) )
		{
			$is_array = TRUE;
			$folders = [];
			foreach($folder_id as $id)
			{
				$folders[] = UUID_TO_BIN($id);
			}
			$this->db->where_in('folder_id',$folders);
		}
		else
		{
			$this->db->where('folder_id',UUID_TO_BIN($folder_id));
		}

		if ($with_draft) {
			$this->db->where("(status in ('" . implode("','", $status) . "') or (status in ('draft', 'waiting-approval') and 
			(documents.owner = 0x". str_replace('-','', $this->auth_user_id) . " or documents.created_by = 0x". str_replace('-','', $this->auth_user_id). " or 
			documents.edited_by = 0x". str_replace('-','', $this->auth_user_id) . " ) ))", NULL, FALSE);
		} else {
			$this->db->where_in('status',$status);
		}

		$q = $this->db
				->select('documents.document_id,folder_id,name,description,last_revised,owner,status,document_type,edited_by,edited_date,
					document_attachment.file_ext,parent_id,
					created_by,created_date,document_category,created,valid_until,
					(SELECT COUNT(*) AS the_count
					FROM document_attachment
					where document_attachment.document_id = documents.document_id and 
					document_attachment.file_name not like CONCAT( LOWER( substring(HEX(documents.document_id), 1, 8) ), \'%\')
					) AS document_attachment_count,
					(SELECT COUNT(DISTINCT document_readlog.user_id) AS the_count
					FROM document_readlog
					where document_readlog.document_id = documents.document_id) AS document_read')
				->from( $this->db_table('documents') )
				->join( $this->db_table('document_attachment') , "(documents.document_id = document_attachment.document_id and 
					document_attachment.file_name like CONCAT( LOWER( substring(HEX(documents.document_id), 1, 8) ), '%'))", 'left')
				->order_by('name', $order_by)
				->get();

		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $document)
			{
				$document->document_id = BIN_TO_UUID($document->document_id);
				$document->parent_id   = BIN_TO_UUID($document->parent_id);
				$document->folder_id   = BIN_TO_UUID($document->folder_id);
				$document->created_by  = BIN_TO_UUID($document->created_by);
				$document->edited_by   = BIN_TO_UUID($document->edited_by);
				$document->document_category   = BIN_TO_UUID($document->document_category);
				// $document->accepted_by = BIN_TO_UUID($document->accepted_by);
				$document->owner       = BIN_TO_UUID($document->owner);

				if( $is_array )
				{
					$documents[$document->folder_id][$document->document_id] = $document;
					$documents['folders'][] = $document->folder_id;
					$documents['version'][] = $document->document_id . '_' . $document->last_revised;
				}
				else
				{
					$documents[$document->document_id] = $document;
				}
			}
		}

		return $documents;
	}

	public function get_document_category($id = NULL)
	{
		$categories = array(null => lang('documents_no_category'));

		if( $id !== NULL )
		{
			$this->db->where('id', UUID_TO_BIN($id));
		}

		$q = $this->db->order_by('name', 'ASC')->get( $this->db_table('document_category') );

		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $category)
			{
				$category->id = BIN_TO_UUID($category->id);
				if( $id !== NULL ) { return $category; }
				$categories[$category->id] = $category->name;
			}
		}

		return $categories;
	}

	public function get_document_category_in_use()
	{
		$categories = array();

		$q = $this->db
				->select('document_category')
				->group_by('document_category')
				->get( $this->db_table('documents') );

		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $category)
			{
				$categories[] = BIN_TO_UUID($category->document_category);
			}
		}

		return $categories;
	}

	// @TODO: Only keep 3 archived documents
	public function save( $document, $status = 'draft', $rights = FALSE )
	{
		// Store a local value, as it gets overwritten
		$document_status = $document->status;
		// var_dump($document);
		$current_time = date('Y-m-d H:i:s');
		// Modify new document
		$document->document_id       = UUID_TO_BIN($document->document_id);
		$document->folder_id         = UUID_TO_BIN($document->folder_id);
		$document->created_by        = UUID_TO_BIN($document->created_by);
		// $document->created_date   = $document->created_date;
		$document->edited_by         = UUID_TO_BIN($this->auth_user_id);
		$document->edited_date       = $current_time;
		if (!empty($this->_get_documents_name())) $document->name = $this->_get_documents_name();
		$document->description       = $this->_get_documents_description();
		$document->created		     = $this->_get_documents_created() ? $this->_get_documents_created() : date('Y-m-d');
		// $document->accepted_by	     = UUID_TO_BIN($this->_get_documents_accepted_by());
		$document->document_type     = $this->_get_documents_type();
		if ($this->_get_documents_category()) {
			$document->document_category = UUID_TO_BIN($this->_get_documents_category());
		}
		$document->last_revised      = $this->_get_documents_last_revised() ? $this->_get_documents_last_revised() : date('Y-m-d');
		$document->valid_until	     = $this->_get_documents_valid_until() ? $this->_get_documents_valid_until() : date('Y-m-d',strtotime("+1 year"));
		$document->reminder		     = $this->_get_documents_reminder();
		$document->content		     = $this->_get_documents_document();
		$document->content_clean     = $this->_get_documents_document_clean();
		// Systemadministratör, owner, and menu owner
		if( $rights )
			$document->owner         = $this->_get_documents_owner();
		if( empty($document->owner) )
			$document->owner         = $this->auth_user_id;
		// Conver to binary
		$document->owner			 = UUID_TO_BIN($document->owner);
		// clone | published | draft | auto-draft | archived | unpublished
		$document->status            = $status;

		// Save old document as archived
		if( isset($document->parent_id) && ! in_array($status, ['draft', 'edit-draft', 'auto-draft', 'auto-save', 'waiting-approval']) )
		{
			// $document->parent_id = UUID_TO_BIN($document->parent_id);
			$q = $this->db->where( 'document_id', $document->document_id )->get( $this->db_table('documents') );

			if( $q->num_rows() === 1 )
			{
				$d = $q->row();
				$d->document_id  = UUID_TO_BIN(UUIDv4());
				$d->parent_id    = $document->document_id;
				$d->created_date = $current_time;
				$d->status       = 'archived';

				$this->db->insert($this->db_table('documents'),$d);
			}

			$document->parent_id = NULL;
			return $this->db->where('document_id', $document->document_id)->update($this->db_table('documents'),$document);
		}
		// Save auto-draft, that never have been published.
		else if( isset($document->parent_id) && in_array($document_status, ['auto-draft-new']) )
		{
			$document->parent_id = NULL;
			return $this->db->where('parent_id', $document->document_id)->update($this->db_table('documents'),$document);
		}
		// Save draft, in case a user denies a document that never have been published.
		else if( isset($document->parent_id) && in_array($document_status, ['draft', 'waiting-approval']) )
		{
			$document->parent_id = NULL;
			return $this->db->where('document_id', $document->document_id)->update($this->db_table('documents'),$document);
		}
		// Save draft
		else if( isset($document->parent_id) )
		{
			$tmp_document_id       = UUID_TO_BIN($document->parent_id);
			$tmp_parent_id         = $document->document_id;
			$document->parent_id   = $tmp_parent_id;
			$document->document_id = $tmp_document_id;

			// Remove draft
			$this->db
				->where('parent_id', $document->parent_id)
				->where_in('status', ['draft','edit-draft', 'waiting-approval', 'auto-save', 'auto-draft'])
				->delete($this->db_table('documents'));

			return $this->db->insert($this->db_table('documents'),$document);
		}
		// Save new document
		else
		{
			$q = $this->db->where( 'document_id', $document->document_id )->get( $this->db_table('documents') );
			if( $q->num_rows() === 1 ) {
				$this->db
				->where('document_id', $document->document_id)
				->where_in('status', ['draft', 'edit-draft', 'auto-draft', 'auto-save', 'waiting-approval'])
				->delete($this->db_table('documents'));
			}
			return $this->db->insert($this->db_table('documents'),$document);
		}

		return FALSE;
	}

	public function delete_autosave($document_id, $user_id, $status)
	{
		return $this->db->delete($this->db_table('documents'), [
			'created_by' => UUID_TO_BIN($user_id),
			'status'     => $status,
			'parent_id'  => UUID_TO_BIN($document_id),
		]);
	}

	public function create_draft($document)
	{
		$current_time = date('Y-m-d H:i:s');
		// Modify new document
		$document->folder_id         = UUID_TO_BIN($document->folder_id);
		$document->created_by        = UUID_TO_BIN($document->created_by);
		// $document->created_date   = $document->created_date;
		$document->edited_by         = UUID_TO_BIN($this->auth_user_id);
		$document->document_category = UUID_TO_BIN($document->document_category);
		$document->owner			 			 = UUID_TO_BIN($document->owner);
		$document->edited_date       = $current_time;
		$document->status            = 'draft';

		// Save draft
		$document->parent_id   = UUID_TO_BIN($document->document_id);
		$document->document_id = UUID_TO_BIN(UUIDv4());

		// Remove draft
		$this->db
			->where('parent_id', $document->parent_id)
			->where_in('status', ['draft','edit-draft', 'waiting-approval', 'auto-save', 'auto-draft'])
			->delete($this->db_table('documents'));

		$this->db->insert($this->db_table('documents'),$document);

	}

	public function create_categories()
	{
		return $this->db->insert($this->db_table('document_category'), [
			'id' => UUID_TO_BIN(UUIDv4()),
			'name' => $this->_get_documents_categories_name()
		]);
	}

	public function update_categories($id)
	{
		return $this->db->update($this->db_table('document_category'), [
			'name' => $this->_get_documents_categories_name()
		],
		[
			'id' => UUID_TO_BIN($id),
		]);
	}

	public function delete_categories($id)
	{
		return $this->db->delete($this->db_table('document_category'), [
			'id' => UUID_TO_BIN($id),
		]);
	}

	public function publish( $document, $upload_path = NULL )
	{
		// var_dump($document);
		$current_time = date('Y-m-d H:i:s');
		// UUUID TO BIN
		$document->document_id       = UUID_TO_BIN($document->document_id);
		$document->folder_id         = UUID_TO_BIN($document->folder_id);
		$document->created_by        = UUID_TO_BIN($document->created_by);
		$document->edited_by         = UUID_TO_BIN($document->edited_by);
		$document->owner			 = UUID_TO_BIN($document->owner);
		$document->document_category = UUID_TO_BIN($document->document_category);
		// clone | published | draft | auto-draft | archived | unpublished
		$document->status            = 'published';

		// var_dump($document);exit;

		// Save old document as archived
		if( isset($document->parent_id) )
		{
			$parent_id = $document->parent_id;
			$document_id = $document->parent_id = UUID_TO_BIN($document->parent_id);
			$q = $this->db->where('document_id', $document->parent_id )->where_in('status', ['published','archived'])->get( $this->db_table('documents') );

			if( $q->num_rows() === 1 )
			{
				$newUUid = UUIDv4();
				$d = $q->row();
				$d->document_id  = UUID_TO_BIN($newUUid);
				$d->parent_id    = $document->parent_id;
				$d->created_date = $current_time;
				$d->status       = 'archived';

				// copy main file for version keeping
				if ($upload_path) {
					$attachment = $this->get_main_attachment($parent_id);
					if($attachment != NULL) {
					$old_file_path = $upload_path . DIRECTORY_SEPARATOR . $attachment->file_name;
					$createExt = pathinfo($old_file_path, PATHINFO_EXTENSION);

					$new_file_path = $upload_path . DIRECTORY_SEPARATOR . $newUUid . '.' . $createExt;
					if(!@copy($old_file_path, $new_file_path))
						log_message('debug', "Copy file error (pubslish) from " . $old_file_path . " to ". $new_file_path);
					}
				}

				$this->db->insert($this->db_table('documents'),$d);
			}

			// Remove draft
			$this->db
				->where('parent_id', $document->parent_id)
				->where_in('status', ['draft', 'edit-draft', 'auto-draft', 'auto-save', 'waiting-approval'])
				->delete($this->db_table('documents'));

			// Update original from draft
			$document->document_id = $document_id;
			$document->parent_id = NULL;
			return $this->db->where('document_id', $document_id)->update($this->db_table('documents'),$document);
		}
		else
		{
			$this->db->where('document_id', $document->document_id)->update($this->db_table('documents'),$document);
		}

		return FALSE;
	}

	public function accepted( $document_id )
	{
		return $this->db->where('document_id', $document_id)->update($this->db_table('documents'), [
			'status' => 'draft'
		]);
	}

	public function update_attachment( $attachment )
	{
		return $this->db->update($this->db_table('document_attachment'),[
			'file_name' => $attachment->file_name,
			'file_ext' => $attachment->file_ext,
		], ['attachment_id' => UUID_TO_BIN($attachment->attachment_id)]);
	}

	public function save_attachments( $attachments )
	{
		// return $this->db->insert_batch($this->db_table('document_attachment'),$attachments);
		return $this->db->insert($this->db_table('document_attachment'),$attachments);
	}

	public function delete_attachments( $attachment_id )
	{
		return $this->db->delete($this->db_table('document_attachment'),array('attachment_id' => UUID_TO_BIN($attachment_id)));
	}

	// @TODO: God mode, delete documents
	// public function remove( $table, $column, $id )
	// {
		// return $this->db->delete($this->db_table($table),array($column => UUID_TO_BIN($id)));
	// }

	public function new_documents_for_user($last_visit) 
	{
		$documents = [];
		$owner = $this->auth_user_id;
		$owner = '0x'.str_replace('-','',$owner);

		$q = $this->db->select('documents.*, menus.owner as menu_owner, folders.name as folder_name, menus.menu_id as menu_id, 
			document_attachment.file_ext, (SELECT COUNT(DISTINCT document_readlog.user_id) AS the_count
			FROM document_readlog
			where document_readlog.document_id = documents.document_id) AS document_read')
				->from( $this->db_table('documents') )
				->join( $this->db_table('folders') , 'documents.folder_id = folders.folder_id', 'left')
				->join( $this->db_table('menus') , 'folders.menu_id = menus.menu_id', 'left')
				->join( $this->db_table('document_editor') , 'documents.document_id = document_editor.document_id', 'left')
				->join( $this->db_table('document_attachment') , "(documents.document_id = document_attachment.document_id and 
					document_attachment.file_name like CONCAT( LOWER( substring(HEX(documents.document_id), 1, 8) ), '%'))", 'left')
				->join( $this->db_table('menu_group') , 'menu_group.menu_id = folders.menu_id', 'left')
				->join( $this->db_table('acl_users_and_groups_table') , "( (acl_users_and_groups.user_id = " . $owner .") and 
					((acl_users_and_groups.group_id = menu_group.group_id)
					or (acl_users_and_groups.object_id in ('folders.folder_id', 'menus.menu_id')))", 'left', NULL, FALSE)
				->join( $this->db_table('acl_actions_table') , 'acl_actions.action_id = acl_users_and_groups.action_id', 'left')
				->where( "documents.name != ''")
				->where('(last_revised > \'' . $last_visit . '\' or
				created_date > \'' . $last_visit . '\' or 
				edited_date > \'' . $last_visit . '\')', Null, false)
				->where('documents.status', 'published');

		if ( !is_role('Systemadministratör'))
		{
			$q = $q->where( "
					(
						menus.menu_id IN (
							SELECT `menu_id` 
							FROM   `menu_group` 
							WHERE  `group_id` IN (
								SELECT `user_group`.`group_id` as `group_id` from user_group 
								left join  `groups` ON `user_group`.`group_id` = `groups`.`group_id` 
								where user_id = " . $owner . " and `groups`.`type` = 'department'
							)
							GROUP  BY `menu_id`
						) 
						AND 
						menus.menu_id  IN (
							SELECT `menu_id` 
							FROM   `menu_group` 
							WHERE  `group_id` IN (
								SELECT `user_group`.`group_id` as `group_id` from user_group 
								left join  `groups` ON `user_group`.`group_id` = `groups`.`group_id` 
								where user_id = " . $owner . " and `groups`.`type` = 'position'
							)
							GROUP  BY `menu_id`
						)
					)", NULL, FALSE)
				->where("(documents.owner = " . $owner . " or
					menus.owner = " . $owner . " or
					acl_actions.action_code in ('read','update') or
					document_editor.user_id = " . $owner . " )", NULL, FALSE);
		}
		$q = $q->order_by('name','ASC')
				->get();

		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $document)
			{
				$document->document_id       = BIN_TO_UUID($document->document_id);
				$document->parent_id         = BIN_TO_UUID($document->parent_id);
				$document->menu_owner        = BIN_TO_UUID($document->menu_owner);
				$document->folder_id         = BIN_TO_UUID($document->folder_id);
				$document->menu_id           = BIN_TO_UUID($document->menu_id);
				$document->created_by        = BIN_TO_UUID($document->created_by);
				$document->edited_by         = BIN_TO_UUID($document->edited_by);
				$document->accepted_by       = BIN_TO_UUID($document->accepted_by);
				$document->document_category = BIN_TO_UUID($document->document_category);
				$document->owner             = BIN_TO_UUID($document->owner);
				$status = in_array($document->status, array('draft', 'edit-draft', 'auto-draft')) ? 'draft' : $document->status;
				if ($document->parent_id == NULL)
					$documents[$document->document_id] = $document;
			}
		}

		return $documents;
	}

  public function document_report_by_owner()
	{
		$owner = '0x'.str_replace('-','',$this->auth_user_id);
		$this->load->driver('cache', array('adapter' => 'file'));
		$res = $this->cache->get(CI_DB_DATABASE . $owner);
		if (!$res) {
			$due_soon = $this->db->select('count(distinct documents.document_id) as count')
				->from( $this->db_table('documents') )
				->join( $this->db_table('folders') , 'documents.folder_id = folders.folder_id', 'left')
				->join( $this->db_table('menus') , 'folders.menu_id = menus.menu_id', 'left')
				->join( $this->db_table('menu_group') , 'menu_group.menu_id = folders.menu_id', 'left')
				->join( $this->db_table('acl_users_and_groups_table') , "( (acl_users_and_groups.user_id = " . $owner .") and 
					((acl_users_and_groups.group_id = menu_group.group_id)
					or (acl_users_and_groups.object_id in ('folders.folder_id', 'menus.menu_id')))", 'left', NULL, FALSE)
				->join( $this->db_table('acl_actions_table') , 'acl_actions.action_id = acl_users_and_groups.action_id', 'left')
				->join( $this->db_table('document_editor') , 'documents.document_id = document_editor.document_id', 'left')
				->where('valid_until <=', date("Y-m-d", strtotime('+1 month')))
				->where('valid_until >', date("Y-m-d"))
				->where('documents.status', 'published')
				->where( "
					(
						menus.menu_id IN (
							SELECT `menu_id` 
							FROM   `menu_group` 
							WHERE  `group_id` IN (
								SELECT `user_group`.`group_id` as `group_id` from user_group 
								left join  `groups` ON `user_group`.`group_id` = `groups`.`group_id` 
								where user_id = " . $owner . " and `groups`.`type` = 'department'
							)
							GROUP  BY `menu_id`
						) 
						AND 
						menus.menu_id  IN (
							SELECT `menu_id` 
							FROM   `menu_group` 
							WHERE  `group_id` IN (
								SELECT `user_group`.`group_id` as `group_id` from user_group 
								left join  `groups` ON `user_group`.`group_id` = `groups`.`group_id` 
								where user_id = " . $owner . " and `groups`.`type` = 'position'
							)
							GROUP  BY `menu_id`
						)
					)", NULL, FALSE)
				->where("(documents.owner = " . $owner . " or
					menus.owner = " . $owner . " or
					acl_actions.action_code in ('read','update') or
					document_editor.user_id = " . $owner . " )", NULL, FALSE)
				->get()->result()[0]->count;
		
			$due = $this->db->select('count(distinct documents.document_id) as count')
				->from( $this->db_table('documents') )
				->join( $this->db_table('folders') , 'documents.folder_id = folders.folder_id', 'left')
				->join( $this->db_table('menus') , 'folders.menu_id = menus.menu_id', 'left')
				->join( $this->db_table('menu_group') , 'menu_group.menu_id = folders.menu_id', 'left')
				->join( $this->db_table('acl_users_and_groups_table') , "( (acl_users_and_groups.user_id = " . $owner .") and 
					((acl_users_and_groups.group_id = menu_group.group_id)
					or (acl_users_and_groups.object_id in ('folders.folder_id', 'menus.menu_id')))", 'left', NULL, FALSE)
				->join( $this->db_table('acl_actions_table') , 'acl_actions.action_id = acl_users_and_groups.action_id', 'left')
				->join( $this->db_table('document_editor') , 'documents.document_id = document_editor.document_id', 'left')
				->where("(valid_until < NOW() and valid_until <> '0000-00-00')")
				->where('documents.status', 'published')
				->where( "
					(
						menus.menu_id IN (
							SELECT `menu_id` 
							FROM   `menu_group` 
							WHERE  `group_id` IN (
								SELECT `user_group`.`group_id` as `group_id` from user_group 
								left join  `groups` ON `user_group`.`group_id` = `groups`.`group_id` 
								where user_id = " . $owner . " and `groups`.`type` = 'department'
							)
							GROUP  BY `menu_id`
						) 
						AND 
						menus.menu_id  IN (
							SELECT `menu_id` 
							FROM   `menu_group` 
							WHERE  `group_id` IN (
								SELECT `user_group`.`group_id` as `group_id` from user_group 
								left join  `groups` ON `user_group`.`group_id` = `groups`.`group_id` 
								where user_id = " . $owner . " and `groups`.`type` = 'position'
							)
							GROUP  BY `menu_id`
						)
					)", NULL, FALSE)
				->where("(documents.owner = " . $owner . " or
					menus.owner = " . $owner . " or
					acl_actions.action_code in ('read','update') or
					document_editor.user_id = " . $owner . " )", NULL, FALSE)
				->get()->result()[0]->count;
			$res = json_encode(array(
				'published'   => 0,
				'due_soon' => $due_soon,
				'due' 	=> $due,
			));
			$this->cache->save(CI_DB_DATABASE . $owner, $res, 3600 * 24);
		} 
		$res = json_decode($res, true);

		$waiting_approval = $this->db->select('count(*) as count')
				->from( $this->db_table('documents') )
				->join( $this->db_table('folders') , 'documents.folder_id = folders.folder_id', 'left')
				->join( $this->db_table('menus') , 'folders.menu_id = menus.menu_id', 'left')
				->join( $this->db_table('document_editor') , 'documents.document_id = document_editor.document_id', 'left')
				->where('(documents.owner = ' . $owner . ' or menus.owner = ' . $owner . ' or document_editor.user_id = ' . $owner . ' )', NULL, FALSE)
				->where('documents.status', 'waiting-approval')->get()->result()[0]->count;

		$working_docs = $this->db->select('count(*) as count')
				->from( $this->db_table('documents') )
				->join( $this->db_table('folders') , 'documents.folder_id = folders.folder_id', 'left')
				->join( $this->db_table('menus') , 'folders.menu_id = menus.menu_id', 'left')
				->join( $this->db_table('document_editor') , 'documents.document_id = document_editor.document_id', 'left')
				->where('(documents.owner = ' . $owner . ' or menus.owner = ' . $owner . ' or document_editor.user_id = ' . $owner . ' )', NULL, FALSE)
				->where( "documents.name != ''")
				->where('documents.status', 'draft')->get()->result()[0]->count;
		
		return array(
			'published'   => $res['published'],
			'due_soon' => $res['due_soon'],
			'due' 	=> $res['due'],
			'working_docs' => $working_docs,
			'waiting_approval' => $waiting_approval
		);
	}

	public function document_report()
	{
		$published = $this->db->select('count(*) as count')
				->from( $this->db_table('documents') )
				->where("(valid_until > DATE_ADD(NOW(), INTERVAL 1 MONTH) or valid_until = '0000-00-00')")
				->where('documents.status', 'published')->get()->result()[0]->count;

		$due_soon = $this->db->select('count(*) as count')
				->from( $this->db_table('documents') )
				->where('valid_until <=', date("Y-m-d", strtotime('+1 month')))
				->where('valid_until >', date("Y-m-d"))
				->where('documents.status', 'published')->get()->result()[0]->count;
		
		$due = $this->db->select('count(*) as count')
				->from( $this->db_table('documents') )
				->where("(valid_until < NOW() and valid_until <> '0000-00-00')")
				->where('documents.status', 'published')->get()->result()[0]->count;

		$waiting_approval = $this->db->select('count(*) as count')
				->from( $this->db_table('documents') )
				->where('documents.status', 'waiting-approval')->get()->result()[0]->count;

		$valid_dates = $this->db->select('valid_until')
				->from( $this->db_table('documents') )
				->where("documents.valid_until <> '0000-00-00'")
				->where('documents.status', 'published')->get()->result();
		
		return array(
			'published'   => $published,
			'due_soon' => $due_soon,
			'due' 	=> $due,
			'waiting_approval' => $waiting_approval,
			'valid_dates' => $valid_dates
		);
	}

	public function search( $search, $groups, $position, $department, $extendedSearch = NULL )
	{
		$company_id = UUID_TO_BIN($this->auth_company_id);
		$documents  = [];
		$menus      = [];

		if( isset($this->menus['all']) )
		{
			$all = array_keys($this->menus['all']);
			foreach($all as $menu)
			{
				$menus[] = UUID_TO_BIN($menu);
			}
		}
		else
		{
			return NULL;
		}

		if( empty($groups) )
		{
			return NULL;
		}

		$query_append = '';

		if( isset($search) && is_array($search) && ! empty($search) )
		{
			foreach($search as $param)
			{
				$query_append = "(documents.name LIKE '%" . $this->db->escape_like_str($param) . "%' ESCAPE '!' OR
									documents.description LIKE '%" . $this->db->escape_like_str($param) . "%' ESCAPE '!' OR
									documents.content_clean LIKE '%" . $this->db->escape_like_str($param) . "%' ESCAPE '!' OR
									documents.content LIKE '%" . $this->db->escape_like_str($param) . "%' ESCAPE '!')";
			}
		}

		$q = $this->db->select('documents.document_id as document_id, documents.name as document_name, documents.description as document_description, 
		documents.content as document_content, documents.created_date as document_created, documents.owner as document_owner, 
		document_category, last_revised as document_last_revised, valid_until as document_valid_until, documents.edited_date as edited_date,
		document_type, folders.name as folder_name, menus.name as menu_name, document_attachment.file_ext')
			->from( $this->db_table('documents') )
			->join( $this->db_table('folders') , 'documents.folder_id = folders.folder_id', 'left')
			->join( $this->db_table('menus') , 'folders.menu_id = menus.menu_id', 'left')
			->join( $this->db_table('document_group') , 'document_group.document_id = documents.document_id', 'left')
			->join( $this->db_table('document_attachment') , "(documents.document_id = document_attachment.document_id and 
					document_attachment.file_name like CONCAT( LOWER( substring(HEX(documents.document_id), 1, 8) ), '%'))", 'left')
			->join( $this->db_table('folder_group') , 'folder_group.folder_id = folders.folder_id', 'left');
		$q = $q->where('(`document_group.group_id` IS NULL OR `document_group.group_id` IN (' . implode(',', $groups) . '))', NULL, FALSE);
		$q = $q->where('(`folder_group.group_id` IS NULL OR `folder_group.group_id` IN (' . implode(',', $groups) . '))', NULL, FALSE);
		$q = $q->where('company_id', $company_id);
		$q = $q->where('documents.status', 'published');
		$q = $q->where_in('menus.menu_id', $menus);
		if( ! empty($query_append) )
		{
			$q = $q->where($query_append);
		}
		if( isset($extendedSearch) && is_array($extendedSearch) && ! empty($extendedSearch) )
		{
			foreach($extendedSearch as $ext)
			{
				if ($ext['column'] == 'documents.valid_until' && $ext['match'] == '>=') {
					$q = $q->where('(' . $ext['column'] . ' ' . $ext['match']. " '" . $ext['find'] . "' or valid_until = '0000-00-00')");
				} elseif ($ext['column'] == 'documents.valid_until' && $ext['match'] == '<=') {
					$q = $q->where('(' . $ext['column'] . ' ' . $ext['match']. " '" . $ext['find'] . "' and valid_until <> '0000-00-00')");
				} else {
					$q = $q->where($ext['column'] . ' ' . $ext['match'], $ext['find']);
				}
			}
		}
		$q = $q->group_by('document_id')->order_by('document_name','ASC');
		$q = $q->get();
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $document)
			{
				$document->document_id = BIN_TO_UUID($document->document_id);
				$document->document_owner = BIN_TO_UUID($document->document_owner);
				$document->document_category = BIN_TO_UUID($document->document_category);
				$documents[] = $document;
			}
		}

		return $documents;
	}

	public function move($document_id, $folder_id)
	{
		return $this->db->update($this->db_table('documents'),['folder_id' => UUID_TO_BIN($folder_id)], ['document_id' => UUID_TO_BIN($document_id)]);
	}

	public function move_multiple()
	{
		$folder_id = $this->input->post('move_document');
		$tree_documents = $this->_get_tree_documents();
		if( empty($tree_documents) OR empty($folder_id) ) { return TRUE; }

		$folder_id = UUID_TO_BIN($folder_id);

		$data = array();
		$delete = array();
		foreach($tree_documents as $document_version)
		{
			list($document_id,$version) = explode('_',$document_version);

			$document_id = UUID_TO_BIN($document_id);
			$data[] = array(
				'document_id' => $document_id,
				'folder_id'   => $folder_id,
			);
			$delete[] = $document_id;
		}

		if( ! empty($data) )
		{
			$this->db->where_in('document_id', $delete)->delete($this->db_table('document_group'));
			return $this->db->update_batch($this->db_table('documents'), $data, 'document_id');
		}

		return FALSE;
	}

	public function copy_multiple()
	{
		$folder_id = $this->input->post('move_document');
		$tree_documents = $this->_get_tree_documents();
		if( empty($tree_documents) OR empty($folder_id) ) { return TRUE; }

		foreach($tree_documents as $document_version)
		{
			list($document_id,$version) = explode('_',$document_version);
			$document = $this->get($document_id);
			$new_document_id = UUIDv4();
			$document->document_id = $new_document_id;
			$document->folder_id = $folder_id;

			$this->save($document, $document->status);
			if(CI_ONLY_OFFICE)
			{
				// @ONLYOFFICE
				$attachment = $this->get_main_attachment($document_id);
				$upload_base_path = CI_UPLOAD_PATH . 'documents';
				$upload_path = $upload_base_path . DIRECTORY_SEPARATOR . $this->auth_company_id;
				if ($attachment) // && is_file($upload_path . DIRECTORY_SEPARATOR . $attachment->file_name))
				{
					$path = $upload_path . DIRECTORY_SEPARATOR . $attachment->file_name;
					$createExt = pathinfo($path, PATHINFO_EXTENSION);
					$new_file_path = $upload_path . DIRECTORY_SEPARATOR . $new_document_id . '.' . $createExt;
					if (is_file($upload_path . DIRECTORY_SEPARATOR . $attachment->file_name) && !file_exists($new_file_path))
					{
						if(!@copy($path, $new_file_path))
						{
							log_message('error', "Copy file error (Copy folder) from " . $path . " to ". $new_file_path);
								//Copy error!!!
						}

						createMeta($new_file_path, $this->auth_user_id, $this->auth_name);
					}
					$attachment_id = UUIDv4();
					$attachment->document_id   = UUID_TO_BIN($new_document_id);
					$attachment->attachment_id = UUID_TO_BIN($attachment_id);
					$attachment->file_name     = $new_document_id . '.' . $createExt;
					$attachment->file_ext      = '.' . $createExt;
					$attachment->uploaded_on   = date("Y-m-d H:i:s");
					$this->save_attachments($attachment);
				}
			}
		}
		
		return TRUE;
	}

	public function history( $document, $status = 'draft' )
	{
		$current_time = date('Y-m-d H:i:s');
		// Modify new document
		$document->document_id       = UUID_TO_BIN($document->document_id);
		$document->parent_id         = UUID_TO_BIN($document->parent_id);
		$document->folder_id         = UUID_TO_BIN($document->folder_id);
		$document->created_by        = UUID_TO_BIN($document->created_by);
		$document->edited_by         = UUID_TO_BIN($this->auth_user_id);
		$document->edited_date       = $current_time;
		$document->document_category = UUID_TO_BIN($document->document_category);
		$document->owner			 = UUID_TO_BIN($document->owner);
		// clone | published | draft | auto-draft | archived | unpublished
		$document->status            = $status;

		// Save old document as archived [FLEX]
		if( isset($document->parent_id) && ! in_array($status, ['draft', 'edit-draft', 'auto-draft']) )
		{
			$q = $this->db->where( 'document_id', $document->parent_id )->get( $this->db_table('documents') );

			if( $q->num_rows() === 1 )
			{
				$d = $q->row();
				$d->document_id  = UUID_TO_BIN(UUIDv4());
				$d->parent_id    = $document->parent_id;
				$d->created_date = $current_time;
				$d->status       = 'archived';

				$this->db->insert($this->db_table('documents'),$d);
			}

			$document->document_id = $parent_id = $document->parent_id;
			$document->parent_id = NULL;
			return $this->db->where('document_id', $parent_id)->update($this->db_table('documents'),$document);
		}
		// Save draft [KIV]
		else if( isset($document->parent_id) )
		{
			return $this->db->insert($this->db_table('documents'),$document);
		}

		return FALSE;
	}

	public function archive($document_id)
	{
		$this->db
		->where('parent_id', UUID_TO_BIN($document_id))
		->where_in('status', ['draft','edit-draft', 'waiting-approval', 'auto-save', 'auto-draft'])
		->delete($this->db_table('documents'));
		return $this->db->update($this->db_table('documents'),['status' => 'unpublished'], ['document_id' => UUID_TO_BIN($document_id)]);
	}

	public function unarchive($document_id)
	{
		return $this->db->update($this->db_table('documents'),['status' => 'published'], ['document_id' => UUID_TO_BIN($document_id)]);
	}

	public function delete_draft($document_id)
	{
		return $this->db
		->where_in('status', ['draft','edit-draft', 'waiting-approval', 'auto-save', 'auto-draft'])
		->delete($this->db_table('documents'),['document_id' => UUID_TO_BIN($document_id)]);
	}

	public function update_edit_date( $document_id )
	{
		return $this->db->update($this->db_table('documents'),['edited_date' => date('Y-m-d H:i:s')], ['document_id' => UUID_TO_BIN($document_id)]);
	}

	public function insert_readlog( $document_id )
	{
		$data = [
			'user_id'     => UUID_TO_BIN($this->auth_user_id),
			'document_id' => UUID_TO_BIN($document_id),
			'done'        => date('Y-m-d H:i:s'),
		];

		return $this->db->insert($this->db_table('document_readlog'), $data);
	}

	public function get_readlog( $document_id, $limit = 100 )
	{
		$log = [];
		$q = $this->db
				->where('document_id',UUID_TO_BIN($document_id))
				->limit($limit)
				->order_by('done', 'DESC')
				->get($this->db_table('document_readlog'));

		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $read)
			{
				$read->user_id = BIN_TO_UUID($read->user_id);
				$log[] = $read;
			}
		}

		return $log;
	}

	public function get_readlog_summary( $document_id )
	{
		$log = [];
		$q = $this->db
				->select('user_id, MAX(done) as done')
				->where('document_id',UUID_TO_BIN($document_id))
				->group_by('user_id,document_id')
				->get($this->db_table('document_readlog'));

		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $read)
			{
				$read->user_id = BIN_TO_UUID($read->user_id);
				$log[] = $read;
			}
		}

		return $log;
	}

	public function documents_editors( $id, $users )
	{
		if( $this->documents_editors_remove($id) === FALSE) { return FALSE; }
		if( empty($users) ) { return TRUE; }
		$data = array();
		foreach($users as $user_id)
		{
			$data[] = array(
				'document_id' => UUID_TO_BIN($id),
				'user_id'     => UUID_TO_BIN($user_id)
			);
		}

		if( ! empty($data) )
			return $this->db->insert_batch($this->db_table('document_editor'),$data);

		return FALSE;
	}

	private function documents_editors_remove( $id )
	{
		return $this->db->delete($this->db_table('document_editor'),array('document_id' => UUID_TO_BIN($id)));
	}

	public function change_owner()
	{
		$from = $this->input->post('documents_owner_from');
		$to = $this->input->post('documents_owner_to');

		if( empty($from) OR empty($to) )
			return NULL;

		if( ! in_array($from, array_keys($this->users_all)) OR ! in_array($to, array_keys($this->users)) )
			return FALSE;

		return $this->db->where('owner', UUID_TO_BIN($from))->update($this->db_table('documents'),[
			'owner' => UUID_TO_BIN($to)
		]);
	}

	public function change_owner_individual()
	{
		$to = $this->input->post('documents_owner_to');
		$tree_documents = $this->_get_tree_documents();
		if( empty($tree_documents) OR empty($to) ) { return TRUE; }

		$to = UUID_TO_BIN($to);

		$data = array();
		foreach($tree_documents as $document_version)
		{
			list($document_id,$version) = explode('_',$document_version);
			$data[] = array(
				'document_id' => UUID_TO_BIN($document_id),
				'owner'       => $to,
			);
		}

		if( ! empty($data) )
			return $this->db->update_batch($this->db_table('documents'), $data, 'document_id');

		return FALSE;
	}

	public function send_to_orna_analys($orna_analys_email, $company_name, $departments) {

		$q = $this->db->select('documents.document_id, documents.name as name, documents.content_clean, document_attachment.file_name')
				->from( $this->db_table('documents') )
				->join( $this->db_table('document_attachment') , "(documents.document_id = document_attachment.document_id and 
					document_attachment.file_name like CONCAT( LOWER( substring(HEX(documents.document_id), 1, 8) ), '%'))", 'left')
				->where('status', 'published')
				->get();

		if( $q->num_rows() !== 0 )
		{
			if (is_dir("/tmp/data")) delDirectory("/tmp/data");
			mkdir("/tmp/data");
			$upload_base_path = CI_UPLOAD_PATH . 'documents';
			$upload_path = $upload_base_path . DIRECTORY_SEPARATOR . $this->auth_company_id;
			foreach($q->result() as $document)
			{
				$filename = $upload_path . DIRECTORY_SEPARATOR . $document->file_name;
				if (is_file($filename)) {
					$fileProperName = "/tmp/data" . DIRECTORY_SEPARATOR . str_replace("/", " ", $document->name) . '§' . $document->file_name;
					if (str_ends_with($document->file_name, 'docx'))
						@copy($filename, $fileProperName);
				} elseif (!empty($document->content_clean)) {
					$fileProperName = "/tmp/data" . DIRECTORY_SEPARATOR . str_replace("/", " ", $document->name) . '§' . BIN_TO_UUID($document->document_id) . ".txt";
					$myfile = fopen($fileProperName, "w");
					fwrite($myfile, $document->content_clean);
					fclose($myfile);
				}
			}

			$q2 = $this->db->select('document_attachment.attachment_id, document_attachment.file_name, document_attachment.file_ext')
			->from( $this->db_table('document_attachment') )
			->join( $this->db_table('documents') , "documents.document_id = document_attachment.document_id", 'left')
			->where('documents.status', 'published')
			->where("document_attachment.file_name not like CONCAT( LOWER( substring(HEX(documents.document_id), 1, 8) ), '%')")
			->get();
			foreach($q2->result() as $attachment)
			{
				$ext = $attachment->file_ext;
				$filename = $upload_path . DIRECTORY_SEPARATOR . BIN_TO_UUID($attachment->attachment_id) . $ext;
				if (is_file($filename)) {
					if ($ext == '.pdf' || $ext == '.docx') {
						$fileProperName = "/tmp/data" . DIRECTORY_SEPARATOR . str_replace("/", " ", $attachment->file_name) . '§' . BIN_TO_UUID($attachment->attachment_id) . $ext;
						@copy($filename, $fileProperName);
					} 
				}
			}
			$file = fopen('/tmp/data/incidents§incidents.csv', 'w');
			$this->deviation_model->write_export_deviation($file, $departments, false);
			fclose($file);
			// add them to zip file
			$rootPath = "/tmp/data";
			$zip = new ZipArchive();
			$zip->open('/tmp/file.zip', ZipArchive::CREATE | ZipArchive::OVERWRITE);
			// Create recursive directory iterator
			$files = new RecursiveIteratorIterator(
				new RecursiveDirectoryIterator($rootPath),
				RecursiveIteratorIterator::LEAVES_ONLY
			);
			foreach ($files as $name => $file)
			{
				// Skip directories (they would be added automatically)
				if (!$file->isDir())
				{
						// Get real and relative path for current file
						$filePath = $file->getRealPath();
						$relativePath = substr($filePath, strlen($rootPath) + 1);
						ini_set('max_execution_time', 3000);
						// Add current file to archive
						$zip->addFile($filePath, str_replace("'", "", $relativePath));
				}
			}
			// Zip archive will be created only after closing object
			$zip->close();

			// send file to orna analysis
			$ch = curl_init();
			$filePath = '/tmp/file.zip';
			$data = array(
				'operations' => '{"operationName":"UploadZipFile","query":"mutation UploadZipFile($file: Upload!, $email: String!, $companyName: String!) { uploadZipFile(file: $file, email: $email, companyName: $companyName) { result }}","variables":{"file":null,"email":"'. $orna_analys_email . '", "companyName": "'. $company_name .'"}}', 
				'map' => '{"0":["variables.file"]}', 
				'0' => curl_file_create($filePath)
			);             
			curl_setopt($ch, CURLOPT_URL, 'https://orna-analys.kvalprak.se/graphql');
			curl_setopt($ch, CURLOPT_POST, 1);
			curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
			$headers = ["X-API-KEY: !0PKMt9b95?lY&i7Hhx`Z{k1pnr@g", "content-type: multipart/form-data", "apollo-require-preflight: true"];
			curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
			$result=curl_exec($ch);
			log_message('error', "Orna Analysis  " . $result);
			curl_close($ch);
		}
	}

}
