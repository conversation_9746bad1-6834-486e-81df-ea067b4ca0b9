# Menu API Documentation

All endpoints require authentication via the Auth API.

## Endpoints

### GET /api/menu
Get all main menus accessible to the authenticated user

**Response:**
```json
{
  "success": true,
  "menus": [
    {
      "id": "01935c8d-c700-7d58-94fe-d25ef0bf76b0",
      "name": "Quality Management",
      "type": "menu"
    },
    {
      "id": "01935c8d-c701-7d58-94fe-d25ef0bf76b1", 
      "name": "Human Resources",
      "type": "menu"
    }
  ],
  "total": 2
}
```

**Error Responses:**
- 401: Unauthorized - Invalid or missing JWT token
- 404: No menus found for user
- 500: Internal server error

---

### GET /api/menu/{menu_id}
Get complete hierarchical structure of a menu including sub-menus, folders, and documents

**Parameters:**
- `menu_id` (UUIDv4): The menu ID to get structure for

**Response:**
```json
{
  "success": true,
  "menu_id": "01935c8d-c700-7d58-94fe-d25ef0bf76b0",
  "items": [
    {
      "id": "01935c8d-c702-7d58-94fe-d25ef0bf76b2",
      "name": "Procedures",
      "type": "menu",
      "parent_id": "01935c8d-c700-7d58-94fe-d25ef0bf76b0",
      "parent_name": "Quality Management",
      "level": 1
    },
    {
      "id": "01935c8d-c703-7d58-94fe-d25ef0bf76b3",
      "name": "Standard Operating Procedures",
      "type": "folder",
      "parent_id": "01935c8d-c702-7d58-94fe-d25ef0bf76b2",
      "parent_name": "Procedures",
      "menu_id": "01935c8d-c702-7d58-94fe-d25ef0bf76b2",
      "menu_name": "Procedures",
      "level": 2
    },
    {
      "id": "01935c8d-c704-7d58-94fe-d25ef0bf76b4",
      "name": "SOP-001 Document Control",
      "type": "document",
      "parent_id": "01935c8d-c703-7d58-94fe-d25ef0bf76b3",
      "parent_name": "Standard Operating Procedures",
      "folder_id": "01935c8d-c703-7d58-94fe-d25ef0bf76b3",
      "folder_name": "Standard Operating Procedures",
      "menu_id": "01935c8d-c702-7d58-94fe-d25ef0bf76b2",
      "menu_name": "Procedures",
      "status": "active",
      "level": 3
    }
  ],
  "total": 3,
  "structure": {
    "sub_menus": 1,
    "folders": 1,
    "documents": 1
  }
}
```

**Item Types:**
- `menu`: Sub-menu under main menu
- `folder`: Folder containing documents  
- `document`: Individual document file

**Error Responses:**
- 400: Invalid or missing menu ID
- 401: Unauthorized - Invalid JWT token
- 404: Menu not found or no access
- 500: Internal server error

---

### GET /api/folder/{folder_id}
Get all documents within a specific folder

**Parameters:**
- `folder_id` (UUIDv4): The folder ID to get documents from

**Response:**
```json
{
  "success": true,
  "folder_id": "01935c8d-c703-7d58-94fe-d25ef0bf76b3",
  "documents": [
    {
      "id": "01935c8d-c704-7d58-94fe-d25ef0bf76b4",
      "name": "SOP-001 Document Control",
      "type": "document",
      "folder_id": "01935c8d-c703-7d58-94fe-d25ef0bf76b3",
      "folder_name": "Standard Operating Procedures",
      "status": "active",
      "created_date": "2024-01-15 10:30:00",
      "edited_date": "2024-02-20 14:45:00",
      "created_by": "<EMAIL>",
      "edited_by": "<EMAIL>"
    },
    {
      "id": "01935c8d-c705-7d58-94fe-d25ef0bf76b5",
      "name": "SOP-002 Risk Assessment", 
      "type": "document",
      "folder_id": "01935c8d-c703-7d58-94fe-d25ef0bf76b3",
      "folder_name": "Standard Operating Procedures",
      "status": "draft",
      "created_date": "2024-02-01 09:15:00",
      "edited_date": null,
      "created_by": "<EMAIL>",
      "edited_by": null
    }
  ],
  "total": 2
}
```

**Document Status Values:**
- `active`: Published and available
- `draft`: Work in progress
- `archived`: No longer active

**Error Responses:**
- 400: Invalid or missing folder ID
- 401: Unauthorized - Invalid JWT token  
- 404: Folder not found or no access
- 500: Database error loading documents

---

### GET /api/document/{document_id}
Get detailed information about a specific document including its location in the menu hierarchy.

In order to open the main attachment (docx, xlsx, pptx) in only office, open a webview with this url 
```
https://view.officeapps.live.com/op/view.aspx?src={base_url}/{main_attachment}
```

To view any other attachment put the `{attachment_id}{file_ext}` in place of the `{main_attachment}` if it is docx, xlsx, or pptx

For pdf or images attachments, you can just put the `{base_url}/{attachment_id}{file_ext}` directly. This link generally downloads the file. So you can directly download the file and display as needed if you have a native viewer in it

**Parameters:**
- `document_id` (UUIDv4): The document ID to retrieve

**Response:**
```json
{
  "success": true,
  "document": {
    "id": "01935c8d-c704-7d58-94fe-d25ef0bf76b4",
    "name": "SOP-001 Document Control",
    "status": "published",
    "created_date": "2024-01-15 10:30:00",
    "created_by":  "John Doe",
    "edited_date": "2024-01-15 10:30:00",
    "edited_by":  "John Doe",
    "description": "...",
    "document_type": "...",
    "document_category": "...",
    "valid_until": "2025-01-15",
    "content": "...", // HTML content, Only to be displayed for old documents. 
    // Old documents don't have main_attachment
    "folder": {
      "id": "01935c8d-c703-7d58-94fe-d25ef0bf76b3",
      "name": "Standard Operating Procedures"
    },
    "parent_menu": {
      "id": "01935c8d-c702-7d58-94fe-d25ef0bf76b2",
      "name": "Quality Management Sub-menu"
    },
    "main_menu": {
      "id": "01935c8d-c700-7d58-94fe-d25ef0bf76b0",
      "name": "Quality Management"
    },
    "attachments": [{
        "attachment_id": "0bc5c7ed-457f-4e18-adce-51a9d0944331",
        "uploaded_on": "2024-03-04 07:34:32",
        "file_name": "01 Specifikation (1).docx",
        "file_ext": ".docx"
      },
      {
        "attachment_id": "a7c76c03-d85b-4879-9f4b-429f5e474ac9",
        "uploaded_on": "2024-03-04 07:34:46",
        "file_name": "2053_1522060450 (1).pdf",
        "file_ext": ".pdf"
      }
    ],
    "base_url": "https://org.orna.vardna.se/documentcallback/download_path/14e217a3-a860-43e0-9874-3d5131d60ed4",
    "main_attachment": "01935c8d-c704-7d58-94fe-d25ef0bf76b4.docx"
  }
}
```

**Document Information:**
- `folder`: Direct parent folder containing the document
- `parent_menu`: Immediate menu containing the folder  
- `main_menu`: Root/top-level menu in the hierarchy
- `main_attachment`: Primary file attachment (null if no main attachment)
- `created_by_name`: Full name of document creator
- `file_ext`: File extension indicating document type (.docx, .pdf, .xlsx, etc.)

**Error Responses:**
- 400: Invalid or missing document ID
- 401: Unauthorized - Invalid JWT token
- 404: Document not found or no access
- 500: Internal server error

## Usage Example

```bash
# 1. Get JWT token first (see login.md)
TOKEN="your-jwt-token"

# 2. Get all main menus
curl -X GET "http://your-domain.com/api/menu" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json"

# 3. Get structure of specific menu
MENU_ID="01935c8d-c700-7d58-94fe-d25ef0bf76b0"
curl -X GET "http://your-domain.com/api/menu/$MENU_ID" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json"

# 4. Get documents in specific folder  
FOLDER_ID="01935c8d-c703-7d58-94fe-d25ef0bf76b3"
curl -X GET "http://your-domain.com/api/folder/$FOLDER_ID" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json"

# 5. Get specific document details
DOCUMENT_ID="01935c8d-c704-7d58-94fe-d25ef0bf76b4"
curl -X GET "http://your-domain.com/api/document/$DOCUMENT_ID" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json"
```

## Notes

- All IDs use UUIDv4 format (36 characters with dashes)
- Hierarchical structure supports up to 3 levels of menus
- User permissions automatically filter accessible content
- All endpoints return JSON responses with consistent error format
- `level` field indicates depth in hierarchy (1 = direct child of main menu)
- Document endpoint includes complete hierarchy context (folder → parent menu → main menu)
- Main attachment contains the primary file associated with the document
